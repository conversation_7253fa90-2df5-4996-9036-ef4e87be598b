import asyncio
"""
Rate limiting module for anti-spam protection.
Tracks message frequency and podcast usage per user.
"""

import time
import threading
from collections import defaultdict, deque
from datetime import datetime, timezone, timedelta
from bot_globals import log_admin
import config


class RateLimiter:
    def __init__(self):
        self.message_times = defaultdict(deque)  # user_id -> deque of message timestamps
        self.podcast_times = defaultdict(list)   # user_id -> list of podcast timestamps
        self.theme_podcast_times = defaultdict(list)  # user_id -> list of theme podcast timestamps

        # New private message limits (only for private chats)

        self.private_podcast_times = defaultdict(list)  # user_id -> list of private podcast timestamps
        self.ai_response_times = defaultdict(list)  # user_id -> list of AI response timestamps
        self.summary_times = defaultdict(list)  # user_id -> list of summary timestamps

        # Video generation limits
        self.video_generation_times = defaultdict(list)  # user_id -> list of video generation timestamps

        



        self.lock = asyncio.Lock()
    
    def check_message_rate(self, user_id):
        """
        Check if user can send a message (anti-spam protection).
        Returns (allowed: bool, should_warn: bool, wait_time: float)

        Logic: Allow normal usage, only block real spam (3+ messages in 2 seconds)
        Admins bypass all rate limits.
        """
        # Check if user is admin - admins bypass all limits
        try:
            from admin_system import is_admin, get_pro_multiplier
            if is_admin(user_id):
                return True, False, 0
        except:
            pass  # If admin system fails, continue with normal checks

        with self.lock:
            current_time = time.time()
            user_times = self.message_times[user_id]

            # Remove old timestamps (older than 10 seconds)
            while user_times and current_time - user_times[0] > 10:
                user_times.popleft()

            # Count recent messages in last 2 seconds
            recent_messages = [t for t in user_times if current_time - t < 2]

            # Only block if user sent 3+ messages in last 2 seconds (real spam)
            if len(recent_messages) >= 3:
                wait_time = 2.0 - (current_time - recent_messages[0])
                return False, True, max(wait_time, 0.5)  # At least 0.5 sec wait

            # Add current timestamp
            user_times.append(current_time)

            # Keep only recent timestamps (last 10 for efficiency)
            while len(user_times) > 10:
                user_times.popleft()

            return True, False, 0
    
    def check_podcast_limit(self, user_id):
        """
        Check if user can request a podcast.
        Returns (allowed: bool, reason: str, wait_time: int)
        Admins bypass all podcast limits.
        """
        # Check if user is admin - admins bypass all limits
        try:
            from admin_system import is_admin, get_pro_multiplier
            # Extract actual user_id if it's a chat-based key like "chat_123"
            actual_user_id = user_id
            if isinstance(user_id, str) and user_id.startswith("chat_"):
                # For chat-based limits, we can't check admin status here
                # Admin bypass will be handled in handlers
                pass
            else:
                if is_admin(actual_user_id):
                    return True, "", 0
        except:
            pass  # If admin system fails, continue with normal checks

        with self.lock:
            current_time = time.time()
            user_podcasts = self.podcast_times[user_id]

            # Remove old podcast timestamps (older than 24 hours)
            user_podcasts[:] = [t for t in user_podcasts if current_time - t < 24 * 3600]

            # Get pro multiplier for this user
            try:
                from admin_system import get_pro_multiplier
                # Extract actual user_id if it's a chat-based key like "chat_123"
                if isinstance(user_id, str) and user_id.startswith("chat_"):
                    multiplier = 1  # For chat-based limits, use normal limits
                else:
                    multiplier = get_pro_multiplier(user_id)
            except:
                multiplier = 1

            # Calculate effective daily limit
            effective_daily_limit = int(config.PODCAST_DAILY_LIMIT * multiplier) if multiplier != float('inf') else float('inf')

            # Check daily limit
            if len(user_podcasts) >= effective_daily_limit:
                oldest_today = min(user_podcasts)
                wait_time = int(24 * 3600 - (current_time - oldest_today))
                return False, f"Превышен дневной лимит ({effective_daily_limit} подкастов в день)", wait_time

            # Check cooldown (6 hours between podcasts)
            if user_podcasts:
                last_podcast = max(user_podcasts)
                time_since_last = current_time - last_podcast
                if time_since_last < config.PODCAST_COOLDOWN:
                    wait_time = int(config.PODCAST_COOLDOWN - time_since_last)
                    hours = wait_time // 3600
                    minutes = (wait_time % 3600) // 60
                    return False, f"Подождите {hours}ч {minutes}м до следующего подкаста", wait_time

            return True, "", 0

    def check_theme_podcast_limit(self, user_id):
        """
        Check if user can request a theme podcast.
        Returns (allowed: bool, reason: str, wait_time: int)
        Limit: 1 theme podcast per user per day.
        Admins bypass all limits.
        """
        # Check if user is admin - admins bypass all limits
        try:
            from admin_system import is_admin, get_pro_multiplier
            if is_admin(user_id):
                return True, "", 0
        except:
            pass  # If admin system fails, continue with normal checks

        with self.lock:
            current_time = time.time()
            user_theme_podcasts = self.theme_podcast_times[user_id]

            # Remove old theme podcast timestamps (older than 24 hours)
            user_theme_podcasts[:] = [t for t in user_theme_podcasts if current_time - t < 24 * 3600]

            # Get pro multiplier for this user
            try:
                from admin_system import get_pro_multiplier
                multiplier = get_pro_multiplier(user_id)
            except:
                multiplier = 1

            # Calculate effective daily limit for theme podcasts
            effective_theme_limit = int(1 * multiplier) if multiplier != float('inf') else float('inf')

            # Check daily limit
            if len(user_theme_podcasts) >= effective_theme_limit:
                oldest_today = min(user_theme_podcasts)
                wait_time = int(24 * 3600 - (current_time - oldest_today))
                return False, f"Превышен дневной лимит тематических подкастов ({effective_theme_limit} в день)", wait_time

            return True, "", 0

    def check_video_generation_limit(self, user_id):
        """
        Check if user can request video generation.
        Returns (allowed: bool, reason: str, wait_time: int)
        Limit: 1 video per 5 minutes per person (10x for pro users).
        Admins bypass all limits.
        """
        # Check if user is admin - admins bypass all limits
        try:
            from admin_system import is_admin, get_pro_multiplier
            if is_admin(user_id):
                return True, "", 0
        except:
            pass  # If admin system fails, continue with normal checks

        with self.lock:
            current_time = time.time()
            user_videos = self.video_generation_times[user_id]

            # Get pro multiplier for this user
            try:
                from admin_system import get_pro_multiplier
                multiplier = get_pro_multiplier(user_id)
            except:
                multiplier = 1

            # Pro users have no time limits for video generation
            if multiplier >= 10:  # Pro users or admins
                return True, "", 0

            # Remove old video timestamps (older than 5 minutes) - only for regular users
            user_videos[:] = [t for t in user_videos if current_time - t < 300]  # 300 seconds = 5 minutes

            # Import config here to avoid circular imports
            try:
                from config import VIDEO_RATE_LIMIT_PER_5_MINUTES
            except ImportError:
                VIDEO_RATE_LIMIT_PER_5_MINUTES = 1  # Fallback value

            # Calculate effective limit (only for regular users)
            effective_limit = VIDEO_RATE_LIMIT_PER_5_MINUTES

            # Check 5-minute limit (only for regular users)
            if len(user_videos) >= effective_limit:
                oldest_in_period = min(user_videos)
                wait_time = int(300 - (current_time - oldest_in_period))  # 300 seconds = 5 minutes
                return False, f"Превышен лимит генерации видео ({effective_limit} видео в 5 минут)", wait_time

            return True, "", 0

    

    def record_video_generation_request(self, user_id):
        """
        Record that user has requested video generation.
        Only record for regular users (pro users have no limits).
        """
        # Get pro multiplier for this user
        try:
            from admin_system import get_pro_multiplier
            multiplier = get_pro_multiplier(user_id)
        except:
            multiplier = 1

        # Don't record requests for pro users since they have no limits
        if multiplier >= 10:  # Pro users or admins
            log_admin(f"Skipped recording video generation request for pro/admin user {user_id}")
            return

        with self.lock:
            current_time = time.time()
            self.video_generation_times[user_id].append(current_time)
            log_admin(f"Recorded video generation request for user {user_id}")

    def get_moscow_midnight_timestamp(self):
        """Get timestamp of last midnight in Moscow timezone (UTC+3)."""
        moscow_tz = timezone(timedelta(hours=3))
        now_moscow = datetime.now(moscow_tz)
        midnight_moscow = now_moscow.replace(hour=0, minute=0, second=0, microsecond=0)
        return midnight_moscow.timestamp()

    def check_private_limit(self, user_id, limit_type, daily_limit):
        """
        Check private message limits (only for private chats).
        Returns (allowed: bool, reason: str, wait_time: int)

        limit_type: 'private_podcast', 'ai_response', 'summary'
        daily_limit: maximum requests per day
        """
        # Check if user is admin - admins bypass all limits
        try:
            from admin_system import is_admin, get_pro_multiplier, get_effective_limit
            if is_admin(user_id):
                return True, "", 0
        except:
            pass  # If admin system fails, continue with normal checks

        with self.lock:
            current_time = time.time()
            midnight_timestamp = self.get_moscow_midnight_timestamp()

            # Get the appropriate times list
            if limit_type == 'private_podcast':
                user_times = self.private_podcast_times[user_id]
                limit_name = "подкастов"
            elif limit_type == 'ai_response':
                user_times = self.ai_response_times[user_id]
                limit_name = "сообщений от ИИ"
            elif limit_type == 'summary':
                user_times = self.summary_times[user_id]
                limit_name = "сводок"
            else:
                return False, "Неизвестный тип ограничения", 0

            # Remove old timestamps (before today's midnight Moscow time)
            user_times[:] = [t for t in user_times if t >= midnight_timestamp]

            # Get effective limit considering admin overrides
            try:
                from admin_system import get_effective_limit
                daily_limit = get_effective_limit(limit_type, daily_limit)
            except:
                pass  # Use original daily_limit if function not available

            # Get pro multiplier for this user
            try:
                from admin_system import get_pro_multiplier
                multiplier = get_pro_multiplier(user_id)
            except:
                multiplier = 1

            # Calculate effective daily limit
            effective_daily_limit = int(daily_limit * multiplier) if multiplier != float('inf') else float('inf')

            # Check daily limit
            if len(user_times) >= effective_daily_limit:
                # Calculate time until next midnight Moscow
                next_midnight = midnight_timestamp + 24 * 3600
                wait_time = int(next_midnight - current_time)

                # Format time until reset
                hours_until_reset = wait_time // 3600
                minutes_until_reset = (wait_time % 3600) // 60

                if hours_until_reset > 0:
                    reset_time = f"{hours_until_reset}ч {minutes_until_reset}м"
                else:
                    reset_time = f"{minutes_until_reset}м"

                return False, f"Превышен дневной лимит {limit_name} ({effective_daily_limit} в день). Лимит сбросится через {reset_time} (в 00:00 МСК)", wait_time

            return True, "", 0

    def record_private_request(self, user_id, limit_type):
        """Record a private message request."""
        with self.lock:
            current_time = time.time()

            if limit_type == 'private_podcast':
                self.private_podcast_times[user_id].append(current_time)
                log_admin(f"Recorded private podcast request for user {user_id}")
            elif limit_type == 'ai_response':
                self.ai_response_times[user_id].append(current_time)
                log_admin(f"Recorded AI response request for user {user_id}")
            elif limit_type == 'summary':
                self.summary_times[user_id].append(current_time)
                log_admin(f"Recorded summary request for user {user_id}")

    def record_podcast_request(self, user_id):
        """
        Record that user has requested a podcast.
        """
        with self.lock:
            current_time = time.time()
            self.podcast_times[user_id].append(current_time)
            log_admin(f"Recorded podcast request for user {user_id}")

    def record_theme_podcast_request(self, user_id):
        """
        Record that user has requested a theme podcast.
        """
        with self.lock:
            current_time = time.time()
            self.theme_podcast_times[user_id].append(current_time)
            log_admin(f"Recorded theme podcast request for user {user_id}")
    
    def get_user_stats(self, user_id):
        """
        Get current stats for a user.
        Returns dict with message and podcast info.
        """
        with self.lock:
            current_time = time.time()
            
            # Clean old data
            user_messages = self.message_times[user_id]
            while user_messages and current_time - user_messages[0] > 60:  # Last minute
                user_messages.popleft()
            
            user_podcasts = self.podcast_times[user_id]
            user_podcasts[:] = [t for t in user_podcasts if current_time - t < 24 * 3600]

            user_theme_podcasts = self.theme_podcast_times[user_id]
            user_theme_podcasts[:] = [t for t in user_theme_podcasts if current_time - t < 24 * 3600]

            return {
                "messages_last_minute": len(user_messages),
                "podcasts_today": len(user_podcasts),
                "theme_podcasts_today": len(user_theme_podcasts),
                "last_message_ago": current_time - user_messages[-1] if user_messages else None,
                "last_podcast_ago": current_time - max(user_podcasts) if user_podcasts else None,
                "last_theme_podcast_ago": current_time - max(user_theme_podcasts) if user_theme_podcasts else None
            }
    
    def cleanup_old_data(self):
        """
        Cleanup old data to prevent memory leaks.
        Should be called periodically.
        """
        with self.lock:
            current_time = time.time()
            
            # Clean message times (keep only last hour)
            for user_id in list(self.message_times.keys()):
                user_times = self.message_times[user_id]
                while user_times and current_time - user_times[0] > 3600:
                    user_times.popleft()
                if not user_times:
                    del self.message_times[user_id]
            
            # Clean podcast times (keep only last 24 hours)
            for user_id in list(self.podcast_times.keys()):
                user_podcasts = self.podcast_times[user_id]
                user_podcasts[:] = [t for t in user_podcasts if current_time - t < 24 * 3600]
                if not user_podcasts:
                    del self.podcast_times[user_id]

            # Clean theme podcast times (keep only last 24 hours)
            for user_id in list(self.theme_podcast_times.keys()):
                user_theme_podcasts = self.theme_podcast_times[user_id]
                user_theme_podcasts[:] = [t for t in user_theme_podcasts if current_time - t < 24 * 3600]
                if not user_theme_podcasts:
                    del self.theme_podcast_times[user_id]

            # Clean video generation times (keep only last 5 minutes)
            for user_id in list(self.video_generation_times.keys()):
                user_videos = self.video_generation_times[user_id]
                user_videos[:] = [t for t in user_videos if current_time - t < 300]  # 300 seconds = 5 minutes
                if not user_videos:
                    del self.video_generation_times[user_id]

            

            # Clean private message limits (keep only last 24 hours)
            midnight_timestamp = self.get_moscow_midnight_timestamp()



            for user_id in list(self.private_podcast_times.keys()):
                user_podcasts = self.private_podcast_times[user_id]
                user_podcasts[:] = [t for t in user_podcasts if t >= midnight_timestamp]
                if not user_podcasts:
                    del self.private_podcast_times[user_id]

            for user_id in list(self.ai_response_times.keys()):
                user_responses = self.ai_response_times[user_id]
                user_responses[:] = [t for t in user_responses if t >= midnight_timestamp]
                if not user_responses:
                    del self.ai_response_times[user_id]

            for user_id in list(self.summary_times.keys()):
                user_summaries = self.summary_times[user_id]
                user_summaries[:] = [t for t in user_summaries if t >= midnight_timestamp]
                if not user_summaries:
                    del self.summary_times[user_id]

            log_admin(f"Rate limiter cleanup completed. Active users: {len(self.message_times)} messages, {len(self.podcast_times)} podcasts, {len(self.theme_podcast_times)} theme podcasts, {len(self.video_generation_times)} video generations, {len(self.private_podcast_times)} private podcasts, {len(self.ai_response_times)} AI responses, {len(self.summary_times)} summaries")


# Global rate limiter instance
rate_limiter = RateLimiter()


def format_time_remaining(seconds):
    """
    Format seconds into human-readable time.
    """
    if seconds < 60:
        return f"{seconds}с"
    elif seconds < 3600:
        minutes = seconds // 60
        return f"{minutes}м"
    else:
        hours = seconds // 3600
        minutes = (seconds % 3600) // 60
        if minutes > 0:
            return f"{hours}ч {minutes}м"
        else:
            return f"{hours}ч"


def rate_limit_check(func):
    """
    Decorator to check rate limits for message handlers.
    """
    def wrapper(message):
        user_id = message.from_user.id

        # Check rate limiting for individual messages (not media groups)
        if not hasattr(message, 'media_group_id') or message.media_group_id is None:
            if not rate_limiter.check_message_rate(user_id):
                user_info = f"user {user_id}"
                if message.from_user.username:
                    user_info += f" (@{message.from_user.username})"
                log_admin(f"{user_info} - Rate limited (1 message per second)")
                # Silently ignore rate-limited messages to avoid spam
                return

        # Call the original handler
        return func(message)

    return wrapper
