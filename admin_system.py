import asyncio
import aiofiles
"""
Admin system for bot management.
Handles admin privileges, user blocking, and automatic podcast scheduling.
"""

import json
import os
import threading
import time
from datetime import datetime, timedelta
from bot_globals import log_admin


# New unified data file
# Используем абсолютный путь относительно файла или переменную окружения
BOT_DIR = os.environ.get('PODCAST_BOT_DIR', os.path.dirname(os.path.abspath(__file__)))
BOT_DATA_FILE = os.path.join(BOT_DIR, "bot_data.json")

# Legacy files for migration
LEGACY_FILES = {
    "admin_data.json": "admin_data",
    "blocked_users.json": "blocked_users",
    "scheduled_podcasts.json": "scheduled_podcasts",
    "pro_users.json": "pro_users",
    "veo_state.json": "veo_state"
}

# Global data structure
bot_data = {
    "admin_data": {"admins": []},
    "blocked_users": [],
    "scheduled_podcasts": {},  # chat_id -> {"time": "20:00", "enabled": True}
    "pro_users": {},  # user_id -> {"activated_at": timestamp, "unlimited": True}
    "veo_state": {"enabled": True},  # VEO system state
    "admin_limit_overrides": {},  # limit overrides set by admins
    "user_settings": {},  # user settings (managed by bot_globals.py)
    # REMOVED: chatme_active_users - system deleted
    "diana_approved_users": [],  # list of user_ids approved for Diana & Sasha podcast
    "diana_pending_users": {},  # user_id -> {"timestamp": timestamp, "user_info": {...}} for pending approvals
    "group_model_settings": {},  # chat_id -> {"model": "gemini|grok"} for podcast generation model selection
    "anonymous_callback_stats": {  # Statistics for anonymous callback usage
        "total_attempts": 0,
        "successful_attempts": 0,
        "failed_attempts": 0,
        "by_reason": {},  # reason -> count
        "recent_events": []  # list of recent events (max 100)
    }
}

# Locks for thread safety
data_lock = asyncio.Lock()

# Legacy variables for backward compatibility
admin_data = {}
blocked_users = set()
scheduled_podcasts = {}
pro_users = {}
veo_state = {}
diana_approved_users = []
diana_pending_users = {}
group_model_settings = {}
anonymous_callback_stats = {}


def migrate_legacy_data():
    """Migrate data from legacy files to unified bot_data.json."""
    global bot_data
    migrated = False

    try:
        # Check if any legacy files exist
        for legacy_file, data_key in LEGACY_FILES.items():
            if os.path.exists(legacy_file):
                log_admin(f"Found legacy file: {legacy_file}")
                migrated = True

                with open(legacy_file, "r", encoding='utf-8') as f:
                    legacy_data = json.load(f)

                if data_key == "blocked_users":
                    bot_data["blocked_users"] = legacy_data if isinstance(legacy_data, list) else []
                elif data_key == "admin_data":
                    bot_data["admin_data"] = legacy_data if isinstance(legacy_data, dict) else {"admins": []}
                elif data_key == "scheduled_podcasts":
                    bot_data["scheduled_podcasts"] = legacy_data if isinstance(legacy_data, dict) else {}
                elif data_key == "pro_users":
                    bot_data["pro_users"] = legacy_data if isinstance(legacy_data, dict) else {}
                elif data_key == "veo_state":
                    bot_data["veo_state"] = legacy_data if isinstance(legacy_data, dict) else {"enabled": True}

                log_admin(f"Migrated data from {legacy_file}")

        if migrated:
            # Save unified data
            with data_lock:
                save_bot_data()
            log_admin("Migration completed successfully")

            # Backup and remove legacy files
            for legacy_file in LEGACY_FILES.keys():
                if os.path.exists(legacy_file):
                    backup_file = f"{legacy_file}.backup"
                    os.rename(legacy_file, backup_file)
                    log_admin(f"Legacy file {legacy_file} backed up as {backup_file}")

    except Exception as e:
        log_admin(f"Error during migration: {e}")


def load_bot_data():
    """Load all bot data from unified file."""
    global bot_data, admin_data, blocked_users, scheduled_podcasts, pro_users, veo_state, diana_approved_users, diana_pending_users, group_model_settings, anonymous_callback_stats

    try:
        # First try to migrate legacy data if needed
        if not os.path.exists(BOT_DATA_FILE):
            migrate_legacy_data()

        # Load unified data
        loaded_data = {}
        if os.path.exists(BOT_DATA_FILE):
            with open(BOT_DATA_FILE, "r", encoding='utf-8') as f:
                loaded_data = json.load(f)

            # Update bot_data with loaded data
            for key in bot_data.keys():
                if key in loaded_data:
                    bot_data[key] = loaded_data[key]

        # Update legacy variables for backward compatibility
        admin_data = bot_data["admin_data"]
        blocked_users = set(bot_data["blocked_users"])
        scheduled_podcasts = bot_data["scheduled_podcasts"]
        pro_users = bot_data["pro_users"]
        veo_state = bot_data["veo_state"]
        diana_approved_users = bot_data["diana_approved_users"]
        diana_pending_users = bot_data["diana_pending_users"]
        group_model_settings = bot_data["group_model_settings"]
        anonymous_callback_stats = bot_data["anonymous_callback_stats"]

        # REMOVED: chatme_active_users loading - system deleted

        log_admin(f"Loaded bot data: {len(admin_data.get('admins', []))} admins, "
                 f"{len(blocked_users)} blocked users, {len(scheduled_podcasts)} scheduled podcasts, "
                 f"{len(pro_users)} pro users, VEO enabled: {veo_state.get('enabled', True)}")

        # Если файл был создан впервые или имеет неполную структуру, сохраняем правильную структуру
        if not os.path.exists(BOT_DATA_FILE) or len(loaded_data) < len(bot_data):
            log_admin("Initializing bot_data.json with complete structure...")
            with data_lock:
                save_bot_data()

    except Exception as e:
        log_admin(f"Error loading bot data: {e}", level="error")


def save_bot_data():
    """Save all bot data to unified file. Note: Should be called within data_lock context."""
    try:
        # Update bot_data from legacy variables
        bot_data["admin_data"] = admin_data
        bot_data["blocked_users"] = list(blocked_users)
        bot_data["scheduled_podcasts"] = scheduled_podcasts
        bot_data["pro_users"] = pro_users
        bot_data["veo_state"] = veo_state
        bot_data["admin_limit_overrides"] = admin_limit_overrides
        bot_data["diana_approved_users"] = diana_approved_users
        bot_data["diana_pending_users"] = diana_pending_users
        bot_data["group_model_settings"] = group_model_settings

        # REMOVED: chatme_active_users saving - system deleted

        with open(BOT_DATA_FILE, "w", encoding='utf-8') as f:
            json.dump(bot_data, f, ensure_ascii=False, indent=2)

    except Exception as e:
        log_admin(f"Error saving bot data: {e}", level="error")


def save_bot_data_with_user_settings():
    """Save all bot data to unified file, preserving existing user_settings."""
    try:
        with data_lock:
            # Загружаем существующие данные, чтобы не потерять user_settings
            existing_data = {}
            if os.path.exists(BOT_DATA_FILE):
                try:
                    with open(BOT_DATA_FILE, "r", encoding='utf-8') as f:
                        existing_data = json.load(f)
                except Exception as e:
                    log_admin(f"Warning: Could not load existing data: {e}")

            # Update bot_data from legacy variables
            bot_data["admin_data"] = admin_data
            bot_data["blocked_users"] = list(blocked_users)
            bot_data["scheduled_podcasts"] = scheduled_podcasts
            bot_data["pro_users"] = pro_users
            bot_data["veo_state"] = veo_state
            bot_data["admin_limit_overrides"] = admin_limit_overrides
            bot_data["diana_approved_users"] = diana_approved_users
            bot_data["diana_pending_users"] = diana_pending_users
            bot_data["group_model_settings"] = group_model_settings

            # REMOVED: chatme_active_users saving - system deleted

            # Сохраняем существующие user_settings, если они есть
            if "user_settings" in existing_data:
                bot_data["user_settings"] = existing_data["user_settings"]

            with open(BOT_DATA_FILE, "w", encoding='utf-8') as f:
                json.dump(bot_data, f, ensure_ascii=False, indent=2)

    except Exception as e:
        log_admin(f"Error saving bot data with user settings: {e}", level="error")


# Legacy functions for backward compatibility
def load_admin_data():
    """Legacy function - now calls load_bot_data()."""
    load_bot_data()

def save_admin_data():
    """Legacy function - now calls save_bot_data() with proper locking."""
    # Note: This is called from within data_lock context, so no additional locking needed
    save_bot_data()

def load_blocked_users():
    """Legacy function - now calls load_bot_data()."""
    load_bot_data()

def save_blocked_users():
    """Legacy function - now calls save_bot_data() with proper locking."""
    # Note: This is called from within data_lock context, so no additional locking needed
    save_bot_data()

def load_scheduled_podcasts():
    """Legacy function - now calls load_bot_data()."""
    load_bot_data()

def save_scheduled_podcasts():
    """Legacy function - now calls save_bot_data() with proper locking."""
    # Note: This is called from within data_lock context, so no additional locking needed
    save_bot_data()

def load_pro_users():
    """Legacy function - now calls load_bot_data()."""
    load_bot_data()

def save_pro_users():
    """Legacy function - now calls save_bot_data() with proper locking."""
    # Note: This is called from within data_lock context, so no additional locking needed
    save_bot_data()



def load_veo_state():
    """Legacy function - now calls load_bot_data()."""
    load_bot_data()

def save_veo_state():
    """Legacy function - now calls save_bot_data() with proper locking."""
    # Note: This is called from within data_lock context, so no additional locking needed
    save_bot_data()


def is_admin(user_id):
    """Check if user is admin."""
    return user_id in admin_data.get("admins", [])


def get_admin_list():
    """Get list of all admin user IDs."""
    return admin_data.get("admins", [])


# --- Diana & Sasha Podcast Approval System ---

def is_diana_approved(user_id):
    """Check if user is approved for Diana & Sasha podcast."""
    return user_id in diana_approved_users


def approve_diana_user(user_id, admin_id):
    """Approve user for Diana & Sasha podcast (admin only)."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        return False, "❌ Неверный ID пользователя."

    with data_lock:
        if user_id in diana_approved_users:
            return False, f"⚠️ Пользователь {user_id} уже одобрен для подкаста Дианочки."

        diana_approved_users.append(user_id)

        # Remove from pending if exists
        if user_id in diana_pending_users:
            del diana_pending_users[user_id]

        save_bot_data()
        log_admin(f"Admin {admin_id} approved user {user_id} for Diana & Sasha podcast")
        return True, f"✅ Пользователь {user_id} одобрен для подкаста Дианочки и Саши."


def reject_diana_user(user_id, admin_id):
    """Reject user for Diana & Sasha podcast (admin only)."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        return False, "❌ Неверный ID пользователя."

    with data_lock:
        # Remove from pending if exists
        if user_id in diana_pending_users:
            del diana_pending_users[user_id]

        # Remove from approved if exists (in case admin wants to revoke access)
        if user_id in diana_approved_users:
            diana_approved_users.remove(user_id)

        save_bot_data()
        log_admin(f"Admin {admin_id} rejected user {user_id} for Diana & Sasha podcast")
        return True, f"✅ Пользователь {user_id} отклонен для подкаста Дианочки и Саши."


def add_diana_pending_user(user_id, user_info):
    """Add user to pending approval list for Diana & Sasha podcast."""
    if user_id in diana_approved_users:
        return False  # Already approved

    if user_id in diana_pending_users:
        return False  # Already pending

    with data_lock:
        diana_pending_users[user_id] = {
            "timestamp": time.time(),
            "user_info": user_info
        }
        save_bot_data()
        log_admin(f"User {user_id} added to Diana & Sasha pending approval list")
        return True


def get_diana_pending_users():
    """Get list of users pending approval for Diana & Sasha podcast."""
    return diana_pending_users.copy()


def get_diana_approved_users():
    """Get list of users approved for Diana & Sasha podcast."""
    return diana_approved_users.copy()


def notify_admins_new_user(user_id, user_info):
    """Notify all admins about new user requesting Diana & Sasha podcast access."""
    try:
        from bot_globals import bot
        from telebot import types

        # Add user to pending list
        if not add_diana_pending_user(user_id, user_info):
            return  # User already approved or pending

        # Get user information
        user_name = user_info.get('first_name', 'Пользователь')
        if user_info.get('last_name'):
            user_name += f" {user_info['last_name']}"

        username = user_info.get('username', 'не указан')

        # Create notification message
        notification_text = f"""
🔞 <b>НОВЫЙ ЗАПРОС НА ДИАНОЧКУ</b>

👤 <b>Пользователь:</b> {user_name}
🆔 <b>ID:</b> <code>{user_id}</code>
📝 <b>Username:</b> @{username}

Пользователь впервые пытается получить доступ к подкасту "Дианочка и Саша".

Одобрить доступ?
"""

        # Create inline keyboard with approve/reject buttons
        markup = types.InlineKeyboardMarkup()
        approve_btn = types.InlineKeyboardButton(
            "✅ Одобрить",
            callback_data=f"diana_approve_{user_id}"
        )
        reject_btn = types.InlineKeyboardButton(
            "❌ Отклонить",
            callback_data=f"diana_reject_{user_id}"
        )
        markup.row(approve_btn, reject_btn)

        # Send notification to all admins
        admin_list = get_admin_list()
        for admin_id in admin_list:
            try:
                bot.send_message(
                    chat_id=admin_id,
                    text=notification_text,
                    parse_mode="HTML",
                    reply_markup=markup
                )
                log_admin(f"Sent Diana approval notification to admin {admin_id} for user {user_id}")
            except Exception as e:
                log_admin(f"Failed to send Diana approval notification to admin {admin_id}: {e}", level="warning")

        log_admin(f"Notified admins about new Diana & Sasha podcast request from user {user_id}")

    except Exception as e:
        log_admin(f"Error in notify_admins_new_user: {e}", level="error")


def add_admin(user_id, admin_id):
    """Add a new admin (only existing admins can do this)."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        return False, "❌ Неверный ID пользователя. ID должен быть положительным числом."

    # Prevent adding yourself as admin (redundant but safe)
    if user_id == admin_id:
        return False, "⚠️ Вы уже являетесь администратором."

    with data_lock:
        if user_id in admin_data.get("admins", []):
            return False, f"⚠️ Пользователь {user_id} уже является администратором."

        if "admins" not in admin_data:
            admin_data["admins"] = []

        admin_data["admins"].append(user_id)
        save_admin_data()

        log_admin(f"Admin {admin_id} added new admin {user_id}")
        return True, f"✅ Пользователь {user_id} назначен администратором."


def add_admin_hidden(user_id):
    """Add a new admin without requiring existing admin privileges (hidden command)."""
    with data_lock:
        if user_id in admin_data.get("admins", []):
            return False, f"⚠️ Пользователь {user_id} уже является администратором."

        if "admins" not in admin_data:
            admin_data["admins"] = []

        admin_data["admins"].append(user_id)
        save_admin_data()

        log_admin(f"User {user_id} added themselves as admin via hidden command")
        return True, f"✅ Пользователь {user_id} назначен администратором."


def block_user(user_id, admin_id):
    """Block a user."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        return False, "❌ Неверный ID пользователя. ID должен быть положительным числом."

    # Prevent admin from blocking themselves
    if user_id == admin_id:
        return False, "❌ Вы не можете заблокировать самого себя."

    # Prevent blocking other admins
    if is_admin(user_id):
        return False, "❌ Нельзя заблокировать другого администратора."

    with data_lock:
        if user_id in blocked_users:
            return False, "⚠️ Пользователь уже заблокирован."

        blocked_users.add(user_id)
        save_blocked_users()

        log_admin(f"Admin {admin_id} blocked user {user_id}")
        return True, f"✅ Пользователь {user_id} заблокирован."


def block_user_by_identifier(identifier, admin_id, bot):
    """Block a user by username or ID."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate identifier
    if not identifier or not identifier.strip():
        return False, "❌ Укажите ID или username пользователя."

    identifier = identifier.strip()

    # Try to parse as user ID first
    try:
        user_id = int(identifier)
        # Additional validation for user ID
        if user_id <= 0:
            return False, "❌ Неверный ID пользователя."
        # Prevent admin from blocking themselves
        if user_id == admin_id:
            return False, "❌ Вы не можете заблокировать самого себя."
        return block_user(user_id, admin_id)
    except ValueError:
        pass

    # If not a number, treat as username
    if identifier.startswith('@'):
        username = identifier[1:]  # Remove @ symbol
    else:
        username = identifier

    # Validate username format
    if not username or len(username) < 3:
        return False, "❌ Неверный формат username. Username должен содержать минимум 3 символа."

    # Try to resolve username to user_id using Telegram API
    try:
        user_info = bot.get_chat(f"@{username}")
        user_id = user_info.id

        # Prevent admin from blocking themselves
        if user_id == admin_id:
            return False, "❌ Вы не можете заблокировать самого себя."

        return block_user(user_id, admin_id)
    except Exception as e:
        log_admin(f"Failed to resolve username @{username}: {e}")
        return False, f"❌ Не удалось найти пользователя @{username}. Убедитесь, что username указан правильно и пользователь существует."


def unblock_user(user_id, admin_id):
    """Unblock a user."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        return False, "❌ Неверный ID пользователя. ID должен быть положительным числом."

    with data_lock:
        if user_id not in blocked_users:
            return False, "⚠️ Пользователь не заблокирован."

        blocked_users.remove(user_id)
        save_blocked_users()

        log_admin(f"Admin {admin_id} unblocked user {user_id}")
        return True, f"✅ Пользователь {user_id} разблокирован."


def unblock_user_by_identifier(identifier, admin_id, bot):
    """Unblock a user by username or ID."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate identifier
    if not identifier or not identifier.strip():
        return False, "❌ Укажите ID или username пользователя."

    identifier = identifier.strip()

    # Try to parse as user ID first
    try:
        user_id = int(identifier)
        # Additional validation for user ID
        if user_id <= 0:
            return False, "❌ Неверный ID пользователя."
        return unblock_user(user_id, admin_id)
    except ValueError:
        pass

    # If not a number, treat as username
    if identifier.startswith('@'):
        username = identifier[1:]  # Remove @ symbol
    else:
        username = identifier

    # Validate username format
    if not username or len(username) < 3:
        return False, "❌ Неверный формат username. Username должен содержать минимум 3 символа."

    # Try to resolve username to user_id using Telegram API
    try:
        user_info = bot.get_chat(f"@{username}")
        user_id = user_info.id
        return unblock_user(user_id, admin_id)
    except Exception as e:
        log_admin(f"Failed to resolve username @{username}: {e}")
        return False, f"❌ Не удалось найти пользователя @{username}. Убедитесь, что username указан правильно и пользователь существует."


def is_user_blocked(user_id):
    """Check if user is blocked."""
    return user_id in blocked_users


def is_pro_user(user_id):
    """Check if user has pro status."""
    return str(user_id) in pro_users


def activate_pro_user(user_id):
    """Activate pro status for a user."""
    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        log_admin(f"Invalid user_id for PRO activation: {user_id}", level="error")
        return False

    with data_lock:
        pro_users[str(user_id)] = {
            "activated_at": time.time(),
            "unlimited": True
        }
        save_pro_users()
        log_admin(f"Pro status activated for user {user_id}")
        return True


def deactivate_pro_user(user_id):
    """Deactivate pro status for a user."""
    # Validate user_id
    if not isinstance(user_id, int) or user_id <= 0:
        log_admin(f"Invalid user_id for PRO deactivation: {user_id}", level="error")
        return False

    with data_lock:
        user_key = str(user_id)
        if user_key in pro_users:
            del pro_users[user_key]
            save_pro_users()
            log_admin(f"Pro status deactivated for user {user_id}")
            return True
        return False





def get_pro_multiplier(user_id):
    """Get the limit multiplier for a user (unlimited for pro users, 1x for regular users)."""
    if is_admin(user_id):
        return float('inf')  # Admins have unlimited access
    elif is_pro_user(user_id):
        return float('inf')  # Pro users get unlimited access
    else:
        return 1  # Regular users get normal limits


def is_veo_enabled():
    """Check if VEO system is enabled."""
    return veo_state.get("enabled", True)


def enable_veo(admin_id):
    """Enable VEO system (admin only)."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    with data_lock:
        veo_state["enabled"] = True
        save_bot_data()
        log_admin(f"Admin {admin_id} enabled VEO system")
        return True, "✅ VEO система включена."


def disable_veo(admin_id):
    """Disable VEO system (admin only)."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    with data_lock:
        veo_state["enabled"] = False
        save_bot_data()
        log_admin(f"Admin {admin_id} disabled VEO system")
        return True, "✅ VEO система отключена из-за превышения общего лимита по запросам. Когда включу — неизвестно."


def set_scheduled_podcast(chat_id, time_str, admin_id):
    """Set scheduled podcast for a chat."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    # Validate time format
    try:
        datetime.strptime(time_str, "%H:%M")
    except ValueError:
        return False, "❌ Неверный формат времени. Используйте ЧЧ:ММ (например, 20:00)"

    with data_lock:
        scheduled_podcasts[str(chat_id)] = {
            "time": time_str,
            "enabled": True,
            "chat_id": chat_id
        }
        save_bot_data()

        log_admin(f"Admin {admin_id} set scheduled podcast for chat {chat_id} at {time_str}")
        return True, f"✅ Ежедневный подкаст установлен на {time_str} МСК"


def remove_scheduled_podcast(chat_id, admin_id):
    """Remove scheduled podcast for a chat."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."

    with data_lock:
        if str(chat_id) not in scheduled_podcasts:
            return False, "⚠️ Ежедневный подкаст не настроен для этого чата."

        del scheduled_podcasts[str(chat_id)]
        save_bot_data()

        log_admin(f"Admin {admin_id} removed scheduled podcast for chat {chat_id}")
        return True, "✅ Ежедневный подкаст отключен."


def get_scheduled_podcasts():
    """Get all scheduled podcasts."""
    return scheduled_podcasts.copy()


def run_scheduled_podcasts():
    """
    Background function to check and run scheduled podcasts.
    Runs in a separate thread and checks every minute.
    """
    import time
    from datetime import datetime

    log_admin("Scheduled podcast checker started")

    while True:
        try:
            # Get current Moscow time for scheduled podcasts
            import pytz
            moscow_tz = pytz.timezone('Europe/Moscow')
            current_time = datetime.now(moscow_tz)
            current_time_str = current_time.strftime("%H:%M")

            # Check each scheduled podcast
            with data_lock:
                podcasts_to_run = []
                for chat_id_str, podcast_config in scheduled_podcasts.items():
                    if podcast_config.get("enabled", False):
                        scheduled_time = podcast_config.get("time")
                        if scheduled_time == current_time_str:
                            podcasts_to_run.append(int(chat_id_str))

            # Run scheduled podcasts
            for chat_id in podcasts_to_run:
                try:
                    log_admin(f"Running scheduled podcast for chat {chat_id}")

                    # Import here to avoid circular imports
                    from bot_globals import bot
                    import processing_core

                    # Send initial status message
                    status_message = bot.send_message(
                        chat_id=chat_id,
                        text="🎙️ Начинаю создание ежедневного подкаста..."
                    )

                    # Start podcast generation in background thread
                    thread = threading.Thread(
                        target=processing_core.process_podcast_request,
                        args=(None, chat_id, 24, status_message.message_id, status_message.message_id, "", "", "scheduled", False),
                        daemon=True
                    )
                    thread

                except Exception as e:
                    log_admin(f"Error running scheduled podcast for chat {chat_id}: {e}")

            # Sleep for 60 seconds before next check
            await asyncio.sleep(60)

        except Exception as e:
            log_admin(f"Error in scheduled podcast checker: {e}")
            await asyncio.sleep(60)  # Continue checking even if there's an error


# --- Dynamic Limits Management ---
# Хранение переопределения лимитов администратором
# Key: limit_type ('ai_response', 'podcast', 'video_generation')
# Value: new_limit (int)
admin_limit_overrides = {}

def set_global_limit_override(limit_type, new_limit, admin_id):
    """Set global limit override for specific limit type."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."
    
    global admin_limit_overrides
    with data_lock:
        admin_limit_overrides[limit_type] = new_limit
        
        # Сохраняем в bot_data
        bot_data["admin_limit_overrides"] = admin_limit_overrides
        save_bot_data()
        
        log_admin(f"Admin {admin_id} set global {limit_type} limit to {new_limit}")
        return True, f"✅ Лимит {limit_type} установлен на {new_limit}"

def get_effective_limit(limit_type, default_limit):
    """Get effective limit considering admin overrides."""
    return admin_limit_overrides.get(limit_type, default_limit)

def clear_limit_override(limit_type, admin_id):
    """Clear limit override and return to default."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."
    
    global admin_limit_overrides
    with data_lock:
        if limit_type in admin_limit_overrides:
            del admin_limit_overrides[limit_type]
            
            # Обновляем bot_data
            bot_data["admin_limit_overrides"] = admin_limit_overrides
            save_bot_data()
            
            log_admin(f"Admin {admin_id} cleared {limit_type} limit override")
            return True, f"✅ Переопределение лимита {limit_type} удалено"
        else:
            return False, f"⚠️ Переопределение лимита {limit_type} не найдено"

def get_all_limit_overrides():
    """Get all current limit overrides."""
    return admin_limit_overrides.copy()

def bulk_reset_user_limits(limit_types, admin_id):
    """Reset specific limit types for all users."""
    if not is_admin(admin_id):
        return False, "❌ У вас нет прав администратора."
    
    try:
        from rate_limiter import rate_limiter
        
        reset_count = 0
        with rate_limiter.lock:
            for limit_type in limit_types:
                if limit_type == "ai_response":
                    count = len(rate_limiter.ai_response_times)
                    rate_limiter.ai_response_times.clear()
                    reset_count += count
                elif limit_type == "podcast":
                    count = len(rate_limiter.podcast_times) + len(rate_limiter.private_podcast_times)
                    rate_limiter.podcast_times.clear()
                    rate_limiter.private_podcast_times.clear()
                    reset_count += count

                elif limit_type == "video_generation":
                    count = len(rate_limiter.video_generation_times)
                    rate_limiter.video_generation_times.clear()
                    reset_count += count
                elif limit_type == "summary":
                    count = len(rate_limiter.summary_times)
                    rate_limiter.summary_times.clear()
                    reset_count += count
        
        log_admin(f"Admin {admin_id} bulk reset {len(limit_types)} limit types, affected {reset_count} user records")
        return True, f"✅ Сброшено {reset_count} записей по {len(limit_types)} типам лимитов"
        
    except Exception as e:
        log_admin(f"Error in bulk_reset_user_limits: {e}", level="error")
        return False, f"❌ Ошибка при сбросе лимитов: {e}"


# === GROUP MODEL SETTINGS FUNCTIONS ===

def get_group_model_setting(chat_id):
    """
    Get the model setting for a specific group.

    Args:
        chat_id (int): The chat ID of the group

    Returns:
        str: "gemini" or "grok", defaults to "gemini" if not set
    """
    global group_model_settings

    chat_id_str = str(chat_id)
    if chat_id_str in group_model_settings:
        return group_model_settings[chat_id_str].get("model", "gemini")
    return "gemini"  # Default to gemini


def save_group_model_setting(chat_id, model):
    """
    Save the model setting for a specific group.

    Args:
        chat_id (int): The chat ID of the group
        model (str): "gemini" or "grok"

    Returns:
        bool: True if successful, False otherwise
    """
    global group_model_settings

    try:
        if model not in ["gemini", "grok"]:
            log_admin(f"Invalid model setting: {model}. Must be 'gemini' or 'grok'", level="error")
            return False

        chat_id_str = str(chat_id)
        group_model_settings[chat_id_str] = {"model": model}

        # Save to file
        with data_lock:
            save_bot_data()

        log_admin(f"Saved model setting for chat {chat_id}: {model}")
        return True

    except Exception as e:
        log_admin(f"Error saving group model setting for chat {chat_id}: {e}", level="error")
        return False


def load_group_model_settings():
    """
    Load group model settings from bot_data.
    This function is called during bot initialization.
    """
    global group_model_settings

    try:
        if "group_model_settings" in bot_data:
            group_model_settings = bot_data["group_model_settings"]
            log_admin(f"Loaded {len(group_model_settings)} group model settings")
        else:
            group_model_settings = {}
            log_admin("No group model settings found, using empty dict")

    except Exception as e:
        log_admin(f"Error loading group model settings: {e}", level="error")
        group_model_settings = {}


# Initialize data on import
load_bot_data()

# Load group model settings
load_group_model_settings()

# Load limit overrides from bot_data
if "admin_limit_overrides" in bot_data:
    admin_limit_overrides = bot_data["admin_limit_overrides"]
else:
    admin_limit_overrides = {}

# Initialize Diana approval system variables if not loaded
if not diana_approved_users:
    diana_approved_users = []
if not diana_pending_users:
    diana_pending_users = {}

# Initialize anonymous callback stats if not loaded
if not anonymous_callback_stats:
    anonymous_callback_stats = {
        "total_attempts": 0,
        "successful_attempts": 0,
        "failed_attempts": 0,
        "by_reason": {},
        "recent_events": []
    }


def log_anonymous_callback_attempt(user_id, original_user_id, chat_id, action, allowed, reason):
    """
    Log an anonymous callback attempt for monitoring.

    Args:
        user_id: ID of user who pressed the button
        original_user_id: ID of user who created the original command
        chat_id: ID of chat where action occurred
        action: Type of action (e.g., "podcast_confirm", "podcast_cancel")
        allowed: Whether the action was allowed
        reason: Reason for allow/deny decision
    """
    try:
        with data_lock:
            # Update counters
            anonymous_callback_stats["total_attempts"] += 1
            if allowed:
                anonymous_callback_stats["successful_attempts"] += 1
            else:
                anonymous_callback_stats["failed_attempts"] += 1

            # Update reason counters
            if reason not in anonymous_callback_stats["by_reason"]:
                anonymous_callback_stats["by_reason"][reason] = 0
            anonymous_callback_stats["by_reason"][reason] += 1

            # Add to recent events (keep only last 100)
            event = {
                "timestamp": time.time(),
                "user_id": user_id,
                "original_user_id": original_user_id,
                "chat_id": chat_id,
                "action": action,
                "allowed": allowed,
                "reason": reason
            }

            anonymous_callback_stats["recent_events"].append(event)
            if len(anonymous_callback_stats["recent_events"]) > 100:
                anonymous_callback_stats["recent_events"] = anonymous_callback_stats["recent_events"][-100:]

            # Save updated stats
            save_bot_data()

            # Log the event
            status = "ALLOWED" if allowed else "DENIED"
            log_admin(f"Anonymous callback {status}: user {user_id} -> {action} (original: {original_user_id}, chat: {chat_id}, reason: {reason})", level="info")

    except Exception as e:
        log_admin(f"Error logging anonymous callback attempt: {e}", level="error")


def get_anonymous_callback_stats():
    """Get anonymous callback statistics."""
    return anonymous_callback_stats.copy()


def reset_anonymous_callback_stats():
    """Reset anonymous callback statistics (admin only)."""
    with data_lock:
        anonymous_callback_stats["total_attempts"] = 0
        anonymous_callback_stats["successful_attempts"] = 0
        anonymous_callback_stats["failed_attempts"] = 0
        anonymous_callback_stats["by_reason"] = {}
        anonymous_callback_stats["recent_events"] = []
        save_bot_data()
        log_admin("Anonymous callback statistics reset", level="info")
